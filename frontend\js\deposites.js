// deposits.js
import { BACKEND_URL } from "./constants.js";
import { showToast } from "./utils.js";

let depositModal, depositSubmitBtn;
let editMode = false;
let editId = null;

let currentSortField = "start";
let currentSortOrder = "desc";
let currentPage = 1;
let pageSize = 10;
let currentSearch = "";

export async function init() {
    depositModal = new bootstrap.Modal(document.getElementById("depositModal"));
    depositSubmitBtn = document.getElementById("submitDepositBtn");

    document.getElementById("addDepositBtn").addEventListener("click", async () => await openDepositForm());
    document.getElementById("depositForm").addEventListener("submit", handleDepositSubmit);
    document.getElementById("searchInput").addEventListener("input", debounce(handleSearch, 400));
    document.getElementById("dateFrom").addEventListener("change", () => loadDeposits(1));
    document.getElementById("dateTo").addEventListener("change", () => loadDeposits(1));
    document.getElementById("exportExcelBtn").addEventListener("click", handleExportExcel);
    document.getElementById("exportCsvBtn").addEventListener("click", handleExportCsv);
    document.getElementById("downloadTemplateBtn").addEventListener("click", handleDownloadTemplate);
    document.getElementById("importExcelInput").addEventListener("change", handleImportExcel);

    document.getElementById("depositsTableBody").addEventListener("click", handleTableClick);

    document.addEventListener("click", function (e) {
      if (e.target.closest(".sort-link")) {
        e.preventDefault();
        const link = e.target.closest(".sort-link");
        const newField = link.dataset.sort;

        if (currentSortField === newField) {
          currentSortOrder = currentSortOrder === "asc" ? "desc" : "asc";
        } else {
          currentSortField = newField;
          currentSortOrder = "asc";
        }

        document.querySelectorAll(".sort-icon").forEach(icon => {
          icon.innerHTML = "";
        });

        const iconTarget = link.querySelector(".sort-icon");
        iconTarget.innerHTML = currentSortOrder === "asc"
          ? '<i class="bi bi-caret-up-fill"></i>'
          : '<i class="bi bi-caret-down-fill"></i>';

        loadDeposits(1, document.getElementById("searchInput").value);
      }
    });

    await loadDeposits();
    await loadInstruments();
  }

async function loadDeposits(page = 1, search = "") {
  const dateFrom = document.getElementById("dateFrom").value;
  const dateTo = document.getElementById("dateTo").value;
  const tableBody = document.getElementById("depositsTableBody");
  tableBody.innerHTML = `<tr><td colspan="11">Se încarcă...</td></tr>`;

  try {
    const token = localStorage.getItem("accessToken");
    const params = new URLSearchParams();

    params.append("page", page);
    params.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);
    if (search) params.append("search", search);
    if (dateFrom) params.append("start_after", dateFrom);
    if (dateTo) params.append("maturity_before", dateTo);

    const url = `${BACKEND_URL}/port/deposits/?${params.toString()}`;
    const res = await fetch(url, {
      headers: { Authorization: `Bearer ${token}` },
    });

    const json = await res.json();
    const data = json.results || [];

    tableBody.innerHTML = "";

    if (data.length === 0) {
      tableBody.innerHTML = `<tr><td colspan="11" class="text-center text-muted">Nu există depozite pentru criteriile selectate.</td></tr>`;
      updatePagination(0, page);
      return;
    }

    data.forEach(deposit => {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td>${deposit.custodian || ''}</td>
        <td>${deposit.deposit || ''}</td>
        <td>${deposit.currency || ''}</td>
        <td>${deposit.deposit || ''}</td>
        <td class="text-end">${parseFloat(deposit.principal || 0).toLocaleString('ro-RO', {minimumFractionDigits: 2})}</td>
        <td class="text-end">${parseFloat(deposit.interest_rate || 0).toFixed(2)}%</td>
        <td>${deposit.start || ''}</td>
        <td>${deposit.maturity || ''}</td>
        <td>${deposit.convention || ''}</td>
        <td class="text-end">${deposit.interest_amount ? parseFloat(deposit.interest_amount).toLocaleString('ro-RO', {minimumFractionDigits: 2}) : ''}</td>
        <td class="text-end">${deposit.interest_calculated ? parseFloat(deposit.interest_calculated).toLocaleString('ro-RO', {minimumFractionDigits: 2}) : ''}</td>
        <td class="text-end">${deposit.check_actual_vs_calc ? parseFloat(deposit.check_actual_vs_calc).toLocaleString('ro-RO', {minimumFractionDigits: 2}) : ''}</td>
        <td class="text-center">${deposit.new_deposit ? '<i class="bi bi-check-circle-fill text-success"></i>' : '<i class="bi bi-x-circle text-muted"></i>'}</td>
        <td class="text-center">${deposit.liquidated ? '<i class="bi bi-check-circle-fill text-danger"></i>' : '<i class="bi bi-x-circle text-muted"></i>'}</td>
        <td>${deposit.details || ''}</td>
        <td class="text-end">
          <button class="btn btn-outline-primary btn-sm me-1 edit-btn" data-id="${deposit.id}" title="Editează">
            <i class="bi bi-pencil"></i>
          </button>
          <button class="btn btn-outline-danger btn-sm delete-btn" data-id="${deposit.id}" title="Șterge">
            <i class="bi bi-trash"></i>
          </button>
        </td>
      `;
      tableBody.appendChild(row);
    });

    updatePagination(json.count, page);
  } catch (error) {
    console.error(error);
    tableBody.innerHTML = `<tr><td colspan="16" class="text-danger">Eroare la încărcarea datelor.</td></tr>`;
  }
}

async function loadInstruments() {
    const token = localStorage.getItem("accessToken");
    const select = document.getElementById("depositInstrument");
    select.innerHTML = "<option value=''>-- Selectează --</option>";
  
    let url = `${BACKEND_URL}/port/instruments/`;
    const allInstruments = [];
  
    try {
      while (url) {
        const res = await fetch(url, {
          headers: { Authorization: `Bearer ${token}` },
        });
        const data = await res.json();
        allInstruments.push(...(data.results || data));
        url = data.next;
      }

      allInstruments.forEach(inst => {
        const option = new Option(inst.symbol, inst.id);
        select.appendChild(option);
      });
    } catch (err) {
      console.error("Eroare la încărcarea instrumentelor", err);
      showToast("Eroare la încărcarea instrumentelor.", "error");
    }
  }

async function openDepositForm(data = null) {
  const form = document.getElementById("depositForm");
  form.reset();

  // Load instruments before showing the modal
  await loadInstruments();

  if (data) {
    Object.keys(data).forEach(key => {
      const input = form.elements[key];
      if (input) {
        if (input.type === "checkbox") {
          input.checked = data[key];
        } else {
          input.value = data[key];
        }
      }
    });
    editMode = true;
    editId = data.id;
  } else {
    editMode = false;
    editId = null;
  }

  depositModal.show();
}

async function handleDepositSubmit(event) {
  event.preventDefault();
  const form = event.target;

  depositSubmitBtn.disabled = true;
  depositSubmitBtn.innerHTML = `<span class="spinner-border spinner-border-sm me-1"></span> Se salvează...`;

  const token = localStorage.getItem("accessToken");
  const formData = new FormData(form);
  const payload = Object.fromEntries(formData.entries());

  payload.new_deposit = form.new_deposit.checked;
  payload.liquidated = form.liquidated.checked;

  const method = editMode ? "PUT" : "POST";
  const url = editMode
    ? `${BACKEND_URL}/port/deposits/${editId}/`
    : `${BACKEND_URL}/port/deposits/`;

  try {
    const res = await fetch(url, {
      method,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`
      },
      body: JSON.stringify(payload),
    });

    const result = await res.json();
    if (res.ok) {
      showToast("Depozitul a fost salvat cu succes.", "success");
      await loadDeposits();
      depositModal.hide();
    } else {
      showToast(result.detail || "Eroare la salvare depozit.", "error");
    }
  } catch (err) {
    console.error(err);
    showToast("Eroare de rețea la salvare depozit.", "error");
  } finally {
    depositSubmitBtn.disabled = false;
    depositSubmitBtn.textContent = editMode ? "Salvează" : "Adaugă";
  }
}

function updatePagination(totalItems, page) {
  const totalPages = Math.ceil(totalItems / pageSize);
  currentPage = page;
  const paginationContainer = document.getElementById("paginationContainer");
  paginationContainer.innerHTML = "";

  if (totalPages <= 1) return;

  const createPageButton = (i, label = null) => {
    const btn = document.createElement("button");
    btn.className = `btn btn-sm ${i === currentPage ? "btn-primary" : "btn-outline-primary"} px-3`;
    btn.innerText = label || i;
    btn.onclick = () => loadDeposits(i, currentSearch);
    return btn;
  };

  const createEllipsis = () => {
    const span = document.createElement("span");
    span.className = "px-3 py-2";
    span.innerText = "...";
    return span;
  };

  if (currentPage > 1) {
    const prevBtn = createPageButton(currentPage - 1, "←");
    prevBtn.onclick = () => loadDeposits(currentPage - 1, currentSearch);
    paginationContainer.appendChild(prevBtn);
  }

  if (totalPages <= 7) {
    for (let i = 1; i <= totalPages; i++) {
      paginationContainer.appendChild(createPageButton(i));
    }
  } else {
    paginationContainer.appendChild(createPageButton(1));
    if (currentPage > 4) paginationContainer.appendChild(createEllipsis());

    const start = Math.max(2, currentPage - 1);
    const end = Math.min(totalPages - 1, currentPage + 1);
    for (let i = start; i <= end; i++) {
      paginationContainer.appendChild(createPageButton(i));
    }

    if (currentPage < totalPages - 3) paginationContainer.appendChild(createEllipsis());
    paginationContainer.appendChild(createPageButton(totalPages));
  }

  if (currentPage < totalPages) {
    const nextBtn = createPageButton(currentPage + 1, "→");
    nextBtn.onclick = () => loadDeposits(currentPage + 1, currentSearch);
    paginationContainer.appendChild(nextBtn);
  }
}

function debounce(fn, delay) {
  let timeoutId;
  return (...args) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn.apply(null, args), delay);
  };
}

function handleSearch(e) {
    currentSearch = e.target.value;
    currentPage = 1;
    loadDeposits(currentPage, currentSearch);
  }

async function handleTableClick(event) {
    const id = event.target.dataset.id;

    if (event.target.classList.contains("edit-btn")) {
      try {
        const token = localStorage.getItem("accessToken");
        const res = await fetch(`${BACKEND_URL}/port/deposits/${id}/`, {
          headers: { Authorization: `Bearer ${token}` },
        });
        const data = await res.json();
        await openDepositForm(data);
      } catch (err) {
        showToast("Nu s-a putut deschide formularul depozitului.", "error");
      }
    }

    if (event.target.classList.contains("delete-btn")) {
      if (!confirm("Ești sigur că vrei să ștergi acest depozit?")) return;

      try {
        const token = localStorage.getItem("accessToken");
        const res = await fetch(`${BACKEND_URL}/port/deposits/${id}/`, {
          method: "DELETE",
          headers: { Authorization: `Bearer ${token}` },
        });

        if (res.ok) {
          showToast("Depozit șters cu succes.", "success");
          await loadDeposits();
        } else {
          showToast("Eroare la ștergerea depozitului.", "error");
        }
      } catch (err) {
        showToast("Eroare de rețea la ștergerea depozitului.", "error");
      }
    }
  }

async function handleExportExcel() {
    const dateFrom = document.getElementById("dateFrom").value;
    const dateTo = document.getElementById("dateTo").value;
    const searchValue = document.getElementById("searchInput").value;
    const token = localStorage.getItem("accessToken");

    const params = new URLSearchParams();

    // Add all current filters that are applied to the table view
    if (dateFrom) params.append("start_after", dateFrom);
    if (dateTo) params.append("maturity_before", dateTo);
    if (searchValue) params.append("search", searchValue);

    // Add current sorting
    params.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);

    const url = `${BACKEND_URL}/port/deposits/export/excel/?${params.toString()}`;

    try {
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) throw new Error("Exportul Excel a eșuat.");

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = "deposits_export.xlsx";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(downloadUrl);

      showToast("Exportul Excel a fost finalizat cu succes.", "success");
    } catch (error) {
      console.error(error);
      showToast("Eroare la exportul Excel.", "error");
    }
  }

  async function handleExportCsv() {
    const dateFrom = document.getElementById("dateFrom").value;
    const dateTo = document.getElementById("dateTo").value;
    const searchValue = document.getElementById("searchInput").value;
    const token = localStorage.getItem("accessToken");

    const params = new URLSearchParams();

    // Add all current filters that are applied to the table view
    if (dateFrom) params.append("start_after", dateFrom);
    if (dateTo) params.append("maturity_before", dateTo);
    if (searchValue) params.append("search", searchValue);

    // Add current sorting
    params.append("ordering", `${currentSortOrder === "desc" ? "-" : ""}${currentSortField}`);

    const url = `${BACKEND_URL}/port/deposits/export/csv/?${params.toString()}`;

    try {
      const response = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` }
      });

      if (!response.ok) throw new Error("Exportul CSV a eșuat.");

      const blob = await response.blob();
      const downloadUrl = window.URL.createObjectURL(blob);
      const a = document.createElement("a");
      a.href = downloadUrl;
      a.download = "deposits_export.csv";
      document.body.appendChild(a);
      a.click();
      a.remove();
      window.URL.revokeObjectURL(downloadUrl);

      showToast("Exportul CSV a fost finalizat cu succes.", "success");
    } catch (error) {
      console.error(error);
      showToast("Eroare la exportul CSV.", "error");
    }
  }

async function handleDownloadTemplate() {
  try {
    const token = localStorage.getItem("accessToken");
    const res = await fetch(`${BACKEND_URL}/port/deposits/template/excel/`, {
      headers: { Authorization: `Bearer ${token}` },
    });

    if (!res.ok) throw new Error("Eroare la descărcarea template-ului.");

    const blob = await res.blob();
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = "deposits_import_template.xlsx";
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);

    showToast("Template-ul a fost descărcat cu succes.", "success");
  } catch (err) {
    console.error(err);
    showToast("Eroare la descărcarea template-ului.", "error");
  }
}

async function handleImportExcel(event) {
  const file = event.target.files[0];
  if (!file) return;

  // Reset file input
  event.target.value = '';

  // Show progress modal
  const importResultsModal = new bootstrap.Modal(document.getElementById("importResultsModal"));
  const importProgress = document.getElementById("importProgress");
  const importResults = document.getElementById("importResults");

  importProgress.classList.remove("d-none");
  importResults.classList.add("d-none");
  importResultsModal.show();

  try {
    const token = localStorage.getItem("accessToken");
    const formData = new FormData();
    formData.append("file", file);

    const res = await fetch(`${BACKEND_URL}/port/deposits/import/excel/`, {
      method: "POST",
      headers: { Authorization: `Bearer ${token}` },
      body: formData,
    });

    const result = await res.json();

    // Hide progress, show results
    importProgress.classList.add("d-none");
    importResults.classList.remove("d-none");

    if (res.ok) {
      // Update counters
      document.getElementById("createdCount").textContent = result.created || 0;
      document.getElementById("updatedCount").textContent = result.updated || 0;
      document.getElementById("errorCount").textContent = result.errors?.length || 0;

      // Show errors if any
      if (result.errors && result.errors.length > 0) {
        const errorsList = document.getElementById("errorsList");
        const errorsListContent = document.getElementById("errorsListContent");

        errorsListContent.innerHTML = "";
        result.errors.forEach(error => {
          const li = document.createElement("li");
          li.textContent = error;
          errorsListContent.appendChild(li);
        });

        errorsList.classList.remove("d-none");
      } else {
        document.getElementById("errorsList").classList.add("d-none");
      }

      // Reload deposits table
      await loadDeposits();

      const totalProcessed = (result.created || 0) + (result.updated || 0);
      if (totalProcessed > 0) {
        showToast(`Import finalizat cu succes: ${totalProcessed} înregistrări procesate.`, "success");
      }
    } else {
      throw new Error(result.error || "Eroare la importul fișierului.");
    }
  } catch (err) {
    console.error(err);
    importProgress.classList.add("d-none");
    importResults.classList.add("d-none");
    importResultsModal.hide();
    showToast(err.message || "Eroare la importul fișierului Excel.", "error");
  }
}
