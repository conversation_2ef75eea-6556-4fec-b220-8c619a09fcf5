<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Dropdown Pagination</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-result { margin: 10px 0; padding: 10px; border-radius: 5px; }
        .success { background-color: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background-color: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background-color: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background-color: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        button { padding: 10px 20px; margin: 5px; background: #007bff; color: white; border: none; border-radius: 5px; cursor: pointer; }
        button:hover { background: #0056b3; }
        #results { margin-top: 20px; }
        .endpoint-test { margin: 15px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
        .endpoint-name { font-weight: bold; color: #0056b3; margin-bottom: 10px; }
        select { margin: 5px; padding: 5px; min-width: 200px; }
    </style>
</head>
<body>
    <h1>🧪 Test Dropdown Pagination</h1>
    <p>This page tests that all dropdown forms properly handle paginated API responses.</p>
    
    <div>
        <button onclick="testAllEndpoints()">Test All Endpoints</button>
        <button onclick="testSpecificEndpoint('instruments')">Test Instruments</button>
        <button onclick="testSpecificEndpoint('currencies')">Test Currencies</button>
        <button onclick="testSpecificEndpoint('custodians')">Test Custodians</button>
        <button onclick="testSpecificEndpoint('accounts')">Test Accounts</button>
        <button onclick="testSpecificEndpoint('operations')">Test Operations</button>
        <button onclick="testSpecificEndpoint('partners')">Test Partners</button>
        <button onclick="testSpecificEndpoint('ubos')">Test UBOs</button>
    </div>
    
    <div id="results"></div>

    <script type="module">
        import { BACKEND_URL } from './frontend/js/constants.js';
        import { loadPaginatedDropdownOptions } from './frontend/js/utils.js';
        
        window.BACKEND_URL = BACKEND_URL;
        window.loadPaginatedDropdownOptions = loadPaginatedDropdownOptions;
        
        function addResult(message, type = 'info') {
            const results = document.getElementById('results');
            const div = document.createElement('div');
            div.className = `test-result ${type}`;
            div.innerHTML = message;
            results.appendChild(div);
        }
        
        // List of endpoints to test
        const endpoints = [
            { name: 'instruments', textField: 'symbol' },
            { name: 'currencies', textField: 'currency_code' },
            { name: 'custodians', textField: 'custodian_code' },
            { name: 'accounts', textField: 'account_code' },
            { name: 'operations', textField: 'operation_code' },
            { name: 'partners', textField: 'partner_code' },
            { name: 'ubos', textField: 'ubo_code' }
        ];
        
        async function testEndpointPagination(endpoint) {
            const token = localStorage.getItem('accessToken');
            if (!token) {
                return {
                    success: false,
                    error: 'No access token found',
                    totalItems: 0,
                    pages: 0
                };
            }
            
            try {
                let url = `${BACKEND_URL}/port/${endpoint}/`;
                let totalItems = 0;
                let pages = 0;
                let hasNextPage = true;
                
                while (hasNextPage && pages < 10) { // Limit to 10 pages to avoid infinite loops
                    const response = await fetch(url, {
                        headers: { 'Authorization': `Bearer ${token}` }
                    });
                    
                    if (!response.ok) {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                    
                    const data = await response.json();
                    const items = data.results || data;
                    totalItems += Array.isArray(items) ? items.length : 0;
                    pages++;
                    
                    url = data.next;
                    hasNextPage = !!url;
                }
                
                return {
                    success: true,
                    totalItems,
                    pages,
                    hasMultiplePages: pages > 1
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    totalItems: 0,
                    pages: 0
                };
            }
        }
        
        async function testDropdownPopulation(endpoint, textField) {
            // Create a test select element
            const testSelectId = `test-select-${endpoint}`;
            const existingSelect = document.getElementById(testSelectId);
            if (existingSelect) {
                existingSelect.remove();
            }
            
            const testSelect = document.createElement('select');
            testSelect.id = testSelectId;
            testSelect.style.display = 'none';
            document.body.appendChild(testSelect);
            
            try {
                const items = await loadPaginatedDropdownOptions(endpoint, testSelectId, {
                    textField: textField
                });
                
                const optionCount = testSelect.options.length - 1; // Subtract 1 for empty option
                
                return {
                    success: true,
                    itemsLoaded: items.length,
                    optionsCreated: optionCount,
                    selectPopulated: optionCount > 0
                };
                
            } catch (error) {
                return {
                    success: false,
                    error: error.message,
                    itemsLoaded: 0,
                    optionsCreated: 0
                };
            } finally {
                // Clean up test element
                testSelect.remove();
            }
        }
        
        window.testSpecificEndpoint = async function(endpointName) {
            const endpoint = endpoints.find(e => e.name === endpointName);
            if (!endpoint) {
                addResult(`❌ Unknown endpoint: ${endpointName}`, 'error');
                return;
            }
            
            addResult(`🔍 Testing ${endpoint.name} endpoint...`, 'info');
            
            // Test pagination
            const paginationResult = await testEndpointPagination(endpoint.name);
            
            if (paginationResult.success) {
                addResult(`
                    <div class="endpoint-test">
                        <div class="endpoint-name">📡 ${endpoint.name.toUpperCase()} API Test</div>
                        ✅ Total items: ${paginationResult.totalItems}<br>
                        ✅ Pages loaded: ${paginationResult.pages}<br>
                        ${paginationResult.hasMultiplePages ? 
                            '✅ Multiple pages detected - pagination working' : 
                            'ℹ️ Single page response (pagination not needed)'}
                    </div>
                `, 'success');
            } else {
                addResult(`
                    <div class="endpoint-test">
                        <div class="endpoint-name">📡 ${endpoint.name.toUpperCase()} API Test</div>
                        ❌ Error: ${paginationResult.error}
                    </div>
                `, 'error');
                return;
            }
            
            // Test dropdown population
            const dropdownResult = await testDropdownPopulation(endpoint.name, endpoint.textField);
            
            if (dropdownResult.success) {
                const isConsistent = dropdownResult.itemsLoaded === dropdownResult.optionsCreated;
                addResult(`
                    <div class="endpoint-test">
                        <div class="endpoint-name">📋 ${endpoint.name.toUpperCase()} Dropdown Test</div>
                        ✅ Items loaded: ${dropdownResult.itemsLoaded}<br>
                        ✅ Options created: ${dropdownResult.optionsCreated}<br>
                        ${isConsistent ? 
                            '✅ Data consistency verified' : 
                            '⚠️ Mismatch between loaded items and created options'}
                    </div>
                `, isConsistent ? 'success' : 'warning');
            } else {
                addResult(`
                    <div class="endpoint-test">
                        <div class="endpoint-name">📋 ${endpoint.name.toUpperCase()} Dropdown Test</div>
                        ❌ Error: ${dropdownResult.error}
                    </div>
                `, 'error');
            }
        };
        
        window.testAllEndpoints = async function() {
            addResult(`🚀 Starting comprehensive dropdown pagination test...`, 'info');
            
            const token = localStorage.getItem('accessToken');
            if (!token) {
                addResult(`❌ No access token found. Please log in first.`, 'error');
                return;
            }
            
            let totalTests = 0;
            let passedTests = 0;
            let paginatedEndpoints = 0;
            
            for (const endpoint of endpoints) {
                totalTests += 2; // API test + dropdown test
                
                // Test API pagination
                const paginationResult = await testEndpointPagination(endpoint.name);
                if (paginationResult.success) {
                    passedTests++;
                    if (paginationResult.hasMultiplePages) {
                        paginatedEndpoints++;
                    }
                }
                
                // Test dropdown population
                const dropdownResult = await testDropdownPopulation(endpoint.name, endpoint.textField);
                if (dropdownResult.success) {
                    passedTests++;
                }
                
                // Add individual results
                await window.testSpecificEndpoint(endpoint.name);
            }
            
            // Summary
            const successRate = Math.round((passedTests / totalTests) * 100);
            const summaryType = successRate === 100 ? 'success' : successRate >= 80 ? 'warning' : 'error';
            
            addResult(`
                <div class="endpoint-test">
                    <div class="endpoint-name">📊 COMPREHENSIVE TEST SUMMARY</div>
                    <strong>Overall Results:</strong><br>
                    ✅ Tests passed: ${passedTests}/${totalTests} (${successRate}%)<br>
                    📄 Endpoints tested: ${endpoints.length}<br>
                    📑 Paginated endpoints: ${paginatedEndpoints}<br>
                    <br>
                    ${successRate === 100 ? 
                        '🎉 All dropdown pagination tests passed!' :
                        successRate >= 80 ?
                        '⚠️ Most tests passed, but some issues detected.' :
                        '❌ Multiple failures detected. Check individual endpoint results.'}
                </div>
            `, summaryType);
        };
        
        // Auto-run a quick check
        addResult(`ℹ️ Ready to test dropdown pagination. Click "Test All Endpoints" to start.`, 'info');
    </script>
</body>
</html>
