import { BACKEND_URL } from "./constants.js";
import { showToast, loadPaginatedDropdownOptions } from "./utils.js";

let instrumentModal;
let editInstrumentId = null;
let currentPage = 1;
let totalPages = 1;
let totalCount = 0;
let pageSize = 20;
let isLoading = false;
let searchTimeout = null;

export async function init() {
  instrumentModal = new bootstrap.Modal(document.getElementById("instrumentModal"));
  document.getElementById("addInstrumentBtn").addEventListener("click", () => openInstrumentForm());
  document.getElementById("instrumentForm").addEventListener("submit", handleInstrumentSubmit);
  document.addEventListener("click", handleInstrumentTableClick);

  await Promise.all([loadCustodians(), loadCurrencies()]);
  await loadInstruments();

  // Add event listeners for filters - reset to page 1 when filters change
  document.getElementById("searchInput").addEventListener("input", () => {
    // Debounce search input to avoid too many API calls
    clearTimeout(searchTimeout);
    searchTimeout = setTimeout(() => {
      currentPage = 1;
      loadInstruments();
    }, 300);
  });
  document.getElementById("filterCustodian").addEventListener("change", () => {
    currentPage = 1;
    loadInstruments();
  });
  document.getElementById("filterCurrency").addEventListener("change", () => {
    currentPage = 1;
    loadInstruments();
  });
  document.getElementById("clearFiltersBtn").addEventListener("click", () => {
    document.getElementById("searchInput").value = "";
    document.getElementById("filterCustodian").value = "";
    document.getElementById("filterCurrency").value = "";
    currentPage = 1;
    loadInstruments();
  });
}

async function loadInstruments() {
  if (isLoading) return;
  isLoading = true;

  const tableBody = document.getElementById("instrumentsTableBody");
  const searchSpinner = document.getElementById("searchSpinner");

  tableBody.innerHTML = `<tr><td colspan="20" class="text-center">Se încarcă...</td></tr>`;
  if (searchSpinner) searchSpinner.classList.remove("d-none");

  try {
    const token = localStorage.getItem("accessToken");

    // Build URL with pagination and filters
    const params = new URLSearchParams();
    params.append('page', currentPage.toString());
    params.append('page_size', pageSize.toString());

    // Add search filter
    const search = document.getElementById("searchInput").value.trim();
    if (search) {
      params.append('search', search);
    }

    // Add custodian filter
    const custodianFilter = document.getElementById("filterCustodian").value;
    if (custodianFilter) {
      params.append('custodian', custodianFilter);
    }

    // Add currency filter
    const currencyFilter = document.getElementById("filterCurrency").value;
    if (currencyFilter) {
      params.append('currency', currencyFilter);
    }

    const url = `${BACKEND_URL}/port/instruments/?${params.toString()}`;
    const res = await fetch(url, {
      headers: { Authorization: `Bearer ${token}` }
    });

    if (!res.ok) {
      throw new Error(`HTTP ${res.status}: ${res.statusText}`);
    }

    const data = await res.json();
    const entries = data.results || [];

    // Update pagination state
    totalCount = data.count || 0;
    totalPages = Math.ceil(totalCount / pageSize);

    // Clear table
    tableBody.innerHTML = "";

    // Render entries
    entries.forEach(entry => {
      const row = document.createElement("tr");
      row.innerHTML = `
        <td>${entry.symbol || ''}</td>
        <td>${entry.isin || ''}</td>
        <td>${entry.custodian_name || ''}</td>
        <td>${entry.currency_name || ''}</td>
        <td>${entry.name || ''}</td>
        <td>${entry.type || ''}</td>
        <td>${entry.principal || ''}</td>
        <td>${entry.face_value || ''}</td>
        <td>${entry.interest || ''}</td>
        <td>${entry.depo_start || ''}</td>
        <td>${entry.bond_issue || ''}</td>
        <td>${entry.bond_first_coupon || ''}</td>
        <td>${entry.maturity || ''}</td>
        <td>${entry.convention || ''}</td>
        <td>${entry.calendar || ''}</td>
        <td>${entry.bond_coupon_count || ''}</td>
        <td>${entry.sector || ''}</td>
        <td>${entry.country || ''}</td>
        <td>${entry.needs_to_be_checked ? "✔" : ""}</td>
        <td class="text-end">
          <button class="btn btn-warning btn-sm edit-instrument-btn" data-id="${entry.id}">Editează</button>
          <button class="btn btn-danger btn-sm delete-instrument-btn" data-id="${entry.id}">Șterge</button>
        </td>
      `;
      tableBody.appendChild(row);
    });

    if (entries.length === 0) {
      tableBody.innerHTML = `<tr><td colspan="20" class="text-center">Niciun rezultat găsit.</td></tr>`;
    }

    // Update pagination controls
    renderPagination();

  } catch (err) {
    console.error('Error loading instruments:', err);
    tableBody.innerHTML = `<tr><td colspan="20" class="text-center text-danger">Eroare la încărcarea datelor: ${err.message}</td></tr>`;
  } finally {
    isLoading = false;
    const searchSpinner = document.getElementById("searchSpinner");
    if (searchSpinner) searchSpinner.classList.add("d-none");
  }
}

function renderPagination() {
  const pagination = document.getElementById("paginationControls");
  if (!pagination) {
    console.error("Pagination container not found");
    return;
  }

  // Calculate pagination info
  const startItem = totalCount > 0 ? (currentPage - 1) * pageSize + 1 : 0;
  const endItem = Math.min(currentPage * pageSize, totalCount);

  // Generate page numbers to show
  const maxVisiblePages = 5;
  let startPage = Math.max(1, currentPage - Math.floor(maxVisiblePages / 2));
  let endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);

  // Adjust start page if we're near the end
  if (endPage - startPage + 1 < maxVisiblePages) {
    startPage = Math.max(1, endPage - maxVisiblePages + 1);
  }

  let paginationHTML = `
    <div class="d-flex align-items-center">
      <span class="text-muted me-3">
        Afișare ${startItem}-${endItem} din ${totalCount} rezultate
      </span>
    </div>
    <nav aria-label="Paginare instrumente">
      <ul class="pagination pagination-sm mb-0">
  `;

  // Previous button
  paginationHTML += `
    <li class="page-item ${currentPage <= 1 ? 'disabled' : ''}">
      <button class="page-link" onclick="goToPage(${currentPage - 1})" ${currentPage <= 1 ? 'disabled' : ''}>
        <i class="bi bi-chevron-left"></i> Anterior
      </button>
    </li>
  `;

  // First page button (if not visible in range)
  if (startPage > 1) {
    paginationHTML += `
      <li class="page-item">
        <button class="page-link" onclick="goToPage(1)">1</button>
      </li>
    `;
    if (startPage > 2) {
      paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
    }
  }

  // Page number buttons
  for (let i = startPage; i <= endPage; i++) {
    paginationHTML += `
      <li class="page-item ${i === currentPage ? 'active' : ''}">
        <button class="page-link" onclick="goToPage(${i})">${i}</button>
      </li>
    `;
  }

  // Last page button (if not visible in range)
  if (endPage < totalPages) {
    if (endPage < totalPages - 1) {
      paginationHTML += `<li class="page-item disabled"><span class="page-link">...</span></li>`;
    }
    paginationHTML += `
      <li class="page-item">
        <button class="page-link" onclick="goToPage(${totalPages})">${totalPages}</button>
      </li>
    `;
  }

  // Next button
  paginationHTML += `
    <li class="page-item ${currentPage >= totalPages ? 'disabled' : ''}">
      <button class="page-link" onclick="goToPage(${currentPage + 1})" ${currentPage >= totalPages ? 'disabled' : ''}>
        Următor <i class="bi bi-chevron-right"></i>
      </button>
    </li>
  `;

  paginationHTML += `
      </ul>
    </nav>
    <div class="d-flex align-items-center">
      <select class="form-select form-select-sm" style="width: auto;" onchange="changePageSize(this.value)">
        <option value="10" ${pageSize === 10 ? 'selected' : ''}>10/pagină</option>
        <option value="20" ${pageSize === 20 ? 'selected' : ''}>20/pagină</option>
        <option value="50" ${pageSize === 50 ? 'selected' : ''}>50/pagină</option>
        <option value="100" ${pageSize === 100 ? 'selected' : ''}>100/pagină</option>
      </select>
    </div>
  `;

  pagination.innerHTML = paginationHTML;
}

// Global functions for pagination (called from HTML)
window.goToPage = function(page) {
  if (page >= 1 && page <= totalPages && page !== currentPage && !isLoading) {
    currentPage = page;
    loadInstruments();
  }
};

window.changePageSize = function(newSize) {
  pageSize = parseInt(newSize);
  currentPage = 1; // Reset to first page when changing page size
  loadInstruments();
};

function openInstrumentForm(data = null) {
  const form = document.getElementById("instrumentForm");
  form.reset();

  if (data) {
    document.getElementById("symbol").value = data.symbol;
    document.getElementById("isin").value = data.isin;
    document.getElementById("custodian").value = data.custodian;
    document.getElementById("currency").value = data.currency;
    document.getElementById("name").value = data.name;
    document.getElementById("type").value = data.type;
    document.getElementById("sector").value = data.sector;
    document.getElementById("country").value = data.country;
    document.getElementById("principal").value = data.principal ?? '';
    document.getElementById("face_value").value = data.face_value ?? 1.0;
    document.getElementById("interest").value = data.interest ?? '';
    document.getElementById("depo_start").value = data.depo_start ?? '';
    document.getElementById("bond_issue").value = data.bond_issue ?? '';
    document.getElementById("bond_first_coupon").value = data.bond_first_coupon ?? '';
    document.getElementById("maturity").value = data.maturity ?? '';
    document.getElementById("convention").value = data.convention ?? '';
    document.getElementById("calendar").value = data.calendar ?? '';
    document.getElementById("bond_coupon_count").value = data.bond_coupon_count ?? '';
    document.getElementById("needs_to_be_checked").value = data.needs_to_be_checked ? 'true' : 'false';
    editInstrumentId = data.id;
  } else {
    editInstrumentId = null;
  }

  instrumentModal.show();
}

async function handleInstrumentSubmit(event) {
  event.preventDefault();
  const token = localStorage.getItem("accessToken");

  const payload = {
    symbol: document.getElementById("symbol").value,
    isin: document.getElementById("isin").value,
    custodian: document.getElementById("custodian").value,
    currency: document.getElementById("currency").value,
    name: document.getElementById("name").value,
    type: document.getElementById("type").value,
    principal: document.getElementById("principal").value || null,
    face_value: document.getElementById("face_value").value || 1.0,
    interest: document.getElementById("interest").value || null,
    depo_start: document.getElementById("depo_start").value || null,
    bond_issue: document.getElementById("bond_issue").value || null,
    bond_first_coupon: document.getElementById("bond_first_coupon").value || null,
    maturity: document.getElementById("maturity").value || null,
    convention: document.getElementById("convention").value,
    calendar: document.getElementById("calendar").value,
    bond_coupon_count: document.getElementById("bond_coupon_count").value || null,
    sector: document.getElementById("sector").value,
    country: document.getElementById("country").value,
    needs_to_be_checked: document.getElementById("needs_to_be_checked").value === "true",
  };
  

  const method = editInstrumentId ? "PUT" : "POST";
  const url = editInstrumentId
    ? `${BACKEND_URL}/port/instruments/${editInstrumentId}/`
    : `${BACKEND_URL}/port/instruments/`;

  try {
    const res = await fetch(url, {
      method,
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payload),
    });

    if (res.ok) {
      showToast("Instrument salvat cu succes.", "success");
      instrumentModal.hide();
      await loadInstruments(); // Reload current page
    } else {
      const err = await res.json();
      showToast(err.detail || "Eroare la salvare instrument.", "error");
    }
  } catch (err) {
    console.error(err);
    showToast("Eroare rețea.", "error");
  }
}

async function handleInstrumentTableClick(e) {
  const editBtn = e.target.closest(".edit-instrument-btn");
  const deleteBtn = e.target.closest(".delete-instrument-btn");

  if (editBtn) {
    const id = editBtn.dataset.id;
    try {
      const token = localStorage.getItem("accessToken");
      const res = await fetch(`${BACKEND_URL}/port/instruments/${id}/`, {
        headers: { Authorization: `Bearer ${token}` },
      });
      const data = await res.json();
      openInstrumentForm(data);
    } catch (err) {
      showToast("Nu s-a putut încărca instrumentul.", "error");
    }
  }

  if (deleteBtn) {
    const id = deleteBtn.dataset.id;
    if (!confirm("Ești sigur că vrei să ștergi acest instrument?")) return;

    try {
      const token = localStorage.getItem("accessToken");
      const res = await fetch(`${BACKEND_URL}/port/instruments/${id}/`, {
        method: "DELETE",
        headers: { Authorization: `Bearer ${token}` },
      });

      if (res.ok) {
        showToast("Instrument șters cu succes.", "success");
        // If we deleted the last item on current page and it's not page 1, go to previous page
        if (totalCount <= (currentPage - 1) * pageSize + 1 && currentPage > 1) {
          currentPage--;
        }
        await loadInstruments();
      } else {
        showToast("Eroare la ștergere instrument.", "error");
      }
    } catch (err) {
      console.error(err);
      showToast("Eroare rețea la ștergere.", "error");
    }
  }
}


async function loadCustodians() {
  try {
    await loadPaginatedDropdownOptions('custodians', ['custodian', 'filterCustodian'], {
      textField: 'custodian_code'
    });
  } catch (err) {
    console.error("Eroare la încărcarea custozilor", err);
    showToast("Eroare la încărcarea custozilor.", "danger");
  }
}

async function loadCurrencies() {
  try {
    await loadPaginatedDropdownOptions('currencies', ['currency', 'filterCurrency'], {
      textField: 'currency_code'
    });
  } catch (err) {
    console.error("Eroare la încărcarea monedelor", err);
    showToast("Eroare la încărcarea monedelor.", "danger");
  }
}

