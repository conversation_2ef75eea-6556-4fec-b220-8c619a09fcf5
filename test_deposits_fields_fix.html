<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Deposits Fields Fix</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .test-section { margin: 20px 0; padding: 15px; border: 1px solid #ddd; }
        .success { color: green; }
        .error { color: red; }
        .info { color: blue; }
        .result { margin: 5px 0; }
    </style>
</head>
<body>
    <h1>Test Deposits Fields Fix</h1>
    <p>This test verifies that the deposits UI and Excel template now include all Deposit model fields.</p>
    
    <div class="test-section">
        <h2>Expected Deposit Model Fields</h2>
        <div id="modelFields"></div>
    </div>
    
    <div class="test-section">
        <h2>API Response Test</h2>
        <button onclick="testAPIResponse()">Test API Response</button>
        <div id="apiResults"></div>
    </div>
    
    <div class="test-section">
        <h2>Excel Template Test</h2>
        <button onclick="testExcelTemplate()">Test Excel Template</button>
        <div id="excelResults"></div>
    </div>
    
    <div class="test-section">
        <h2>UI Table Columns Test</h2>
        <button onclick="testUIColumns()">Test UI Table Columns</button>
        <div id="uiResults"></div>
    </div>

    <script>
        const BACKEND_URL = 'http://localhost:8000';
        
        // Expected model fields based on the Deposit model
        const expectedModelFields = [
            'id',
            'deposit',
            'principal', 
            'interest_rate',
            'start',
            'maturity',
            'convention',
            'interest_amount',
            'interest_calculated',  // Previously missing
            'check_actual_vs_calc', // Previously missing
            'new_deposit',
            'liquidated',
            'details'
        ];
        
        // Expected UI table columns
        const expectedUIColumns = [
            'Instrument',
            'Principal',
            'Rată (%)',
            'Data Start', 
            'Maturitate',
            'Convenție',
            'Sumă Dobândă',
            'Dobândă Calculată',     // Added
            'Verificare Act vs Calc', // Added
            'Depozit Nou',
            'Lichidat',
            'Detalii',
            'Acțiuni'
        ];
        
        // Expected Excel template fields
        const expectedExcelFields = [
            'deposit_symbol',
            'principal',
            'interest_rate',
            'start',
            'maturity',
            'convention',
            'interest_amount',
            'interest_calculated',   // Added
            'check_actual_vs_calc',  // Added
            'new_deposit',
            'liquidated',
            'details'
        ];
        
        function addResult(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.textContent = message;
            return div;
        }
        
        // Display expected model fields
        document.getElementById('modelFields').innerHTML = expectedModelFields.map(field => 
            `<div class="result info">• ${field}</div>`
        ).join('');
        
        async function testAPIResponse() {
            const resultsDiv = document.getElementById('apiResults');
            resultsDiv.innerHTML = '<div class="result info">Testing API response...</div>';
            
            try {
                const response = await fetch(`${BACKEND_URL}/port/deposits/?page_size=1`);
                
                if (!response.ok) {
                    resultsDiv.appendChild(addResult(`❌ API request failed: ${response.status}`, 'error'));
                    return;
                }
                
                const data = await response.json();
                
                if (!data.results || data.results.length === 0) {
                    resultsDiv.appendChild(addResult('ℹ️ No deposits found in database', 'info'));
                    return;
                }
                
                const deposit = data.results[0];
                const actualFields = Object.keys(deposit);
                
                resultsDiv.appendChild(addResult('✅ API response received successfully', 'success'));
                resultsDiv.appendChild(addResult(`Fields in response: ${actualFields.join(', ')}`, 'info'));
                
                // Check if all expected fields are present
                const missingFields = expectedModelFields.filter(field => !actualFields.includes(field));
                const extraFields = actualFields.filter(field => !expectedModelFields.includes(field) && !['custodian', 'currency'].includes(field));
                
                if (missingFields.length === 0) {
                    resultsDiv.appendChild(addResult('✅ All expected model fields are present in API response', 'success'));
                } else {
                    resultsDiv.appendChild(addResult(`❌ Missing fields in API response: ${missingFields.join(', ')}`, 'error'));
                }
                
                if (extraFields.length > 0) {
                    resultsDiv.appendChild(addResult(`ℹ️ Additional fields in API response: ${extraFields.join(', ')}`, 'info'));
                }
                
                // Check specifically for the new fields
                if (actualFields.includes('interest_calculated')) {
                    resultsDiv.appendChild(addResult('✅ interest_calculated field is present', 'success'));
                } else {
                    resultsDiv.appendChild(addResult('❌ interest_calculated field is missing', 'error'));
                }
                
                if (actualFields.includes('check_actual_vs_calc')) {
                    resultsDiv.appendChild(addResult('✅ check_actual_vs_calc field is present', 'success'));
                } else {
                    resultsDiv.appendChild(addResult('❌ check_actual_vs_calc field is missing', 'error'));
                }
                
            } catch (error) {
                resultsDiv.appendChild(addResult(`❌ Error testing API: ${error.message}`, 'error'));
            }
        }
        
        async function testExcelTemplate() {
            const resultsDiv = document.getElementById('excelResults');
            resultsDiv.innerHTML = '<div class="result info">Testing Excel template...</div>';
            
            try {
                const response = await fetch(`${BACKEND_URL}/port/deposits/template/excel/`);
                
                if (!response.ok) {
                    resultsDiv.appendChild(addResult(`❌ Excel template request failed: ${response.status}`, 'error'));
                    return;
                }
                
                resultsDiv.appendChild(addResult('✅ Excel template downloaded successfully', 'success'));
                resultsDiv.appendChild(addResult('ℹ️ Template should include: ' + expectedExcelFields.join(', '), 'info'));
                resultsDiv.appendChild(addResult('ℹ️ Please manually verify the template includes interest_calculated and check_actual_vs_calc columns', 'info'));
                
            } catch (error) {
                resultsDiv.appendChild(addResult(`❌ Error testing Excel template: ${error.message}`, 'error'));
            }
        }
        
        async function testUIColumns() {
            const resultsDiv = document.getElementById('uiResults');
            resultsDiv.innerHTML = '<div class="result info">Testing UI table columns...</div>';
            
            try {
                // This would need to be tested in the actual UI context
                resultsDiv.appendChild(addResult('ℹ️ Expected UI columns:', 'info'));
                expectedUIColumns.forEach(col => {
                    resultsDiv.appendChild(addResult(`  • ${col}`, 'info'));
                });
                
                resultsDiv.appendChild(addResult('ℹ️ Please manually verify the UI table includes the new columns:', 'info'));
                resultsDiv.appendChild(addResult('  • Dobândă Calculată (interest_calculated)', 'info'));
                resultsDiv.appendChild(addResult('  • Verificare Act vs Calc (check_actual_vs_calc)', 'info'));
                
            } catch (error) {
                resultsDiv.appendChild(addResult(`❌ Error testing UI: ${error.message}`, 'error'));
            }
        }
    </script>
</body>
</html>
