export function showToast(message, type = "primary") {
  const toastEl = document.getElementById("appToast");
  const toastBody = document.getElementById("toastMessage");

  if (!toastEl || !toastBody) {
    console.warn("Toast element not found.");
    return;
  }

  toastEl.classList.remove("bg-primary", "bg-danger", "bg-success", "bg-warning");
  toastEl.classList.add(`bg-${type}`);
  toastBody.textContent = message;

  const toast = new bootstrap.Toast(toastEl, { delay: 3000 });
  toast.show();
}

/**
 * Load paginated dropdown options from an API endpoint
 * @param {string} endpoint - The API endpoint (e.g., 'instruments', 'currencies')
 * @param {string|Array} selectIds - ID(s) of select element(s) to populate
 * @param {Object} options - Configuration options
 * @param {string} options.valueField - Field to use for option value (default: 'id')
 * @param {string} options.textField - Field to use for option text
 * @param {string} options.emptyText - Text for empty option (default: '-- Selectează --')
 * @param {boolean} options.showLoading - Show loading state (default: true)
 * @returns {Promise<Array>} Array of loaded items
 */
export async function loadPaginatedDropdownOptions(endpoint, selectIds, options = {}) {
  const {
    valueField = 'id',
    textField = null,
    emptyText = '-- Selectează --',
    showLoading = true
  } = options;

  // Ensure selectIds is an array
  const selects = Array.isArray(selectIds) ? selectIds : [selectIds];
  const selectElements = selects.map(id => document.getElementById(id)).filter(Boolean);

  if (selectElements.length === 0) {
    console.warn(`No select elements found for IDs: ${selects.join(', ')}`);
    return [];
  }

  try {
    const token = localStorage.getItem("accessToken");
    if (!token) {
      throw new Error("No access token found");
    }

    // Import BACKEND_URL dynamically
    const { BACKEND_URL } = await import('./constants.js');

    // Show loading state
    if (showLoading) {
      selectElements.forEach(select => {
        select.innerHTML = `<option value="">Se încarcă...</option>`;
        select.disabled = true;
      });
    }

    // Handle paginated responses - load all pages
    let url = `${BACKEND_URL}/port/${endpoint}/`;
    const allItems = [];

    while (url) {
      const res = await fetch(url, {
        headers: { Authorization: `Bearer ${token}` },
      });

      if (!res.ok) {
        throw new Error(`HTTP error! status: ${res.status}`);
      }

      const data = await res.json();
      allItems.push(...(data.results || data));
      url = data.next; // Move to next page if available
    }

    // Populate select elements
    selectElements.forEach(select => {
      select.innerHTML = `<option value="">${emptyText}</option>`;

      allItems.forEach(item => {
        const option = document.createElement("option");
        option.value = item[valueField];

        // Determine text to display
        let displayText = '';
        if (textField) {
          displayText = item[textField];
        } else {
          // Auto-detect common text fields
          displayText = item.name || item.code || item.label || item.symbol ||
                       item.custodian_code || item.account_code || item.operation_code ||
                       item.partner_code || item.currency_code || item.ubo_code ||
                       item[valueField];
        }

        option.textContent = displayText;
        select.appendChild(option);
      });

      select.disabled = false;
    });

    return allItems;

  } catch (err) {
    console.error(`Error loading ${endpoint}:`, err);

    // Show error state
    selectElements.forEach(select => {
      select.innerHTML = `<option value="">Eroare la încărcare</option>`;
      select.disabled = false;
    });

    showToast(`Eroare la încărcarea opțiunilor pentru ${endpoint}.`, "danger");
    return [];
  }
}